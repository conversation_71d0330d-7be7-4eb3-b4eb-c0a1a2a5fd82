@echo off
echo === GPAce Codebase Modularization Analysis ===
echo.

echo 1. HTML FILES ANALYSIS:
echo.
for %%f in (*.html) do (
    echo   📄 %%f
    if "%%f"=="tasks.html" echo      → Suggested Module: TASK MANAGEMENT
    if "%%f"=="priority-calculator.html" echo      → Suggested Module: TASK MANAGEMENT
    if "%%f"=="priority-list.html" echo      → Suggested Module: TASK MANAGEMENT
    if "%%f"=="academic-details.html" echo      → Suggested Module: ACADEMIC MANAGEMENT
    if "%%f"=="subject-marks.html" echo      → Suggested Module: ACADEMIC MANAGEMENT
    if "%%f"=="daily-calendar.html" echo      → Suggested Module: CALENDAR
    if "%%f"=="flashcards.html" echo      → Suggested Module: STUDY TOOLS
    if "%%f"=="study-spaces.html" echo      → Suggested Module: STUDY TOOLS
    if "%%f"=="workspace.html" echo      → Suggested Module: STUDY TOOLS
    if "%%f"=="grind.html" echo      → Suggested Module: STUDY TOOLS
    if "%%f"=="instant-test-feedback.html" echo      → Suggested Module: ASSESSMENT
    if "%%f"=="sleep-saboteurs.html" echo      → Suggested Module: WELLNESS
    if "%%f"=="settings.html" echo      → Suggested Module: CONFIGURATION
    if "%%f"=="index.html" echo      → Suggested Module: CORE APPLICATION
    if "%%f"=="landing.html" echo      → Suggested Module: CORE APPLICATION
    echo.
)

echo 2. JAVASCRIPT FILES ANALYSIS:
echo.
for %%f in (*.js) do (
    echo   📜 %%f
    if "%%f"=="priority-calculator.js" echo      → Category: TASK MANAGEMENT
    if "%%f"=="priority-calculator-with-worker.js" echo      → Category: TASK MANAGEMENT
    if "%%f"=="worker.js" echo      → Category: WORKER/BACKGROUND
    if "%%f"=="test-worker.js" echo      → Category: WORKER/BACKGROUND
    if "%%f"=="server.js" echo      → Category: SERVER/INFRASTRUCTURE
    echo.
)

echo 3. DIRECTORY STRUCTURE ANALYSIS:
echo.
for /d %%d in (*) do (
    echo   📁 %%d/
    if "%%d"=="css" echo      → Status: ALREADY MODULARIZED ✓
    if "%%d"=="data" echo      → Status: CONFIGURATION DATA ✓
    if "%%d"=="functions" echo      → Status: FIREBASE FUNCTIONS ✓
    if "%%d"=="assets" echo      → Status: STATIC RESOURCES ✓
    if "%%d"=="docs" echo      → Status: DOCUMENTATION ✓
    if "%%d"=="flashcards" echo      → Status: FEATURE MODULE ✓
    echo.
)

echo 4. MODULARIZATION RECOMMENDATIONS:
echo.
echo   🎯 Task Management Module:
echo      ✓ tasks.html
echo      ✓ priority-calculator.html
echo      ✓ priority-list.html
echo      ✓ priority-calculator.js
echo      ✓ priority-calculator-with-worker.js
echo.
echo   🎯 Academic Management Module:
echo      ✓ academic-details.html
echo      ✓ subject-marks.html
echo.
echo   🎯 Calendar System Module:
echo      ✓ daily-calendar.html
echo.
echo   🎯 Study Tools Module:
echo      ✓ flashcards.html
echo      ✓ study-spaces.html
echo      ✓ workspace.html
echo      ✓ grind.html
echo.
echo   🎯 Assessment Module:
echo      ✓ instant-test-feedback.html
echo.
echo   🎯 Wellness Module:
echo      ✓ sleep-saboteurs.html
echo.
echo   🎯 Configuration Module:
echo      ✓ settings.html
echo.
echo   🎯 Core Application Module:
echo      ✓ index.html
echo      ✓ landing.html
echo      ✓ 404.html
echo      ✓ extracted.html
echo.

echo 5. SUGGESTED MIGRATION COMMANDS:
echo.
echo # Create new directory structure
echo mkdir src\modules\tasks src\modules\academic src\modules\calendar src\modules\study src\modules\assessment src\modules\wellness src\modules\config src\core src\shared
echo.
echo # Move Task Management files
echo move tasks.html src\modules\tasks\
echo move priority-calculator.html src\modules\tasks\
echo move priority-list.html src\modules\tasks\
echo move priority-calculator.js src\modules\tasks\
echo move priority-calculator-with-worker.js src\modules\tasks\
echo.
echo # Move Academic Management files
echo move academic-details.html src\modules\academic\
echo move subject-marks.html src\modules\academic\
echo.
echo # Move other modules...
echo # (See modularization-plan.md for complete migration steps)
echo.

echo Analysis complete! Check 'modularization-plan.md' for detailed implementation plan.
pause
