# GPAce Codebase Analysis Script
# This script analyzes the current codebase structure and generates detailed modularization recommendations

Write-Host "=== GPAce Codebase Modularization Analysis ===" -ForegroundColor Green
Write-Host ""

# Function to analyze file dependencies
function Analyze-FileDependencies {
    param([string]$FilePath)
    
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw
        $dependencies = @()
        
        # Look for script tags, link tags, and imports
        $scriptMatches = [regex]::Matches($content, '<script[^>]*src=["\']([^"\']*)["\']')
        $linkMatches = [regex]::Matches($content, '<link[^>]*href=["\']([^"\']*)["\']')
        $importMatches = [regex]::Matches($content, 'import.*from ["\']([^"\']*)["\']')
        
        foreach ($match in $scriptMatches) {
            $dependencies += "JS: " + $match.Groups[1].Value
        }
        foreach ($match in $linkMatches) {
            $dependencies += "CSS: " + $match.Groups[1].Value
        }
        foreach ($match in $importMatches) {
            $dependencies += "MODULE: " + $match.Groups[1].Value
        }
        
        return $dependencies
    }
    return @()
}

# 1. Analyze HTML files and their purposes
Write-Host "1. HTML FILES ANALYSIS:" -ForegroundColor Yellow
$htmlFiles = Get-ChildItem -Path "." -Filter "*.html" | Sort-Object Name

foreach ($file in $htmlFiles) {
    Write-Host "  📄 $($file.Name)" -ForegroundColor Cyan
    
    # Analyze file content for purpose
    $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
    if ($content) {
        # Extract title
        $titleMatch = [regex]::Match($content, '<title[^>]*>([^<]*)</title>')
        if ($titleMatch.Success) {
            Write-Host "     Title: $($titleMatch.Groups[1].Value)" -ForegroundColor Gray
        }
        
        # Count dependencies
        $deps = Analyze-FileDependencies -FilePath $file.FullName
        Write-Host "     Dependencies: $($deps.Count)" -ForegroundColor Gray
        
        # Look for main functionality indicators
        if ($content -match "task|priority") {
            Write-Host "     → Suggested Module: TASK MANAGEMENT" -ForegroundColor Green
        }
        elseif ($content -match "academic|grade|subject|mark") {
            Write-Host "     → Suggested Module: ACADEMIC MANAGEMENT" -ForegroundColor Green
        }
        elseif ($content -match "calendar|schedule|daily") {
            Write-Host "     → Suggested Module: CALENDAR" -ForegroundColor Green
        }
        elseif ($content -match "flashcard|study|workspace") {
            Write-Host "     → Suggested Module: STUDY TOOLS" -ForegroundColor Green
        }
        elseif ($content -match "test|feedback|assessment") {
            Write-Host "     → Suggested Module: ASSESSMENT" -ForegroundColor Green
        }
        elseif ($content -match "sleep|wellness|health") {
            Write-Host "     → Suggested Module: WELLNESS" -ForegroundColor Green
        }
        elseif ($content -match "setting|config|preference") {
            Write-Host "     → Suggested Module: CONFIGURATION" -ForegroundColor Green
        }
        else {
            Write-Host "     → Suggested Module: CORE APPLICATION" -ForegroundColor Yellow
        }
    }
    Write-Host ""
}

# 2. Analyze JavaScript files
Write-Host "2. JAVASCRIPT FILES ANALYSIS:" -ForegroundColor Yellow
$jsFiles = Get-ChildItem -Path "." -Filter "*.js" | Sort-Object Name

foreach ($file in $jsFiles) {
    Write-Host "  📜 $($file.Name)" -ForegroundColor Cyan
    $size = [math]::Round($file.Length / 1KB, 2)
    Write-Host "     Size: $size KB" -ForegroundColor Gray
    
    # Categorize JS files
    if ($file.Name -match "worker") {
        Write-Host "     → Category: WORKER/BACKGROUND" -ForegroundColor Magenta
    }
    elseif ($file.Name -match "server") {
        Write-Host "     → Category: SERVER/INFRASTRUCTURE" -ForegroundColor Magenta
    }
    elseif ($file.Name -match "priority|calculator") {
        Write-Host "     → Category: TASK MANAGEMENT" -ForegroundColor Green
    }
    else {
        Write-Host "     → Category: UTILITY/SHARED" -ForegroundColor Yellow
    }
    Write-Host ""
}

# 3. Analyze directory structure
Write-Host "3. DIRECTORY STRUCTURE ANALYSIS:" -ForegroundColor Yellow
$directories = Get-ChildItem -Path "." -Directory | Sort-Object Name

foreach ($dir in $directories) {
    $fileCount = (Get-ChildItem -Path $dir.FullName -Recurse -File -ErrorAction SilentlyContinue | Measure-Object).Count
    Write-Host "  📁 $($dir.Name)/ ($fileCount files)" -ForegroundColor Magenta
    
    # Analyze directory purpose
    switch ($dir.Name.ToLower()) {
        "css" { Write-Host "     → Status: ALREADY MODULARIZED ✓" -ForegroundColor Green }
        "data" { Write-Host "     → Status: CONFIGURATION DATA ✓" -ForegroundColor Green }
        "functions" { Write-Host "     → Status: FIREBASE FUNCTIONS ✓" -ForegroundColor Green }
        "assets" { Write-Host "     → Status: STATIC RESOURCES ✓" -ForegroundColor Green }
        "docs" { Write-Host "     → Status: DOCUMENTATION ✓" -ForegroundColor Green }
        "flashcards" { Write-Host "     → Status: FEATURE MODULE ✓" -ForegroundColor Green }
        default { Write-Host "     → Status: NEEDS REVIEW" -ForegroundColor Yellow }
    }
    Write-Host ""
}

# 4. Generate modularization recommendations
Write-Host "4. MODULARIZATION RECOMMENDATIONS:" -ForegroundColor Yellow
Write-Host ""

$recommendations = @{
    "Task Management" = @("tasks.html", "priority-calculator.html", "priority-list.html", "priority-calculator.js")
    "Academic Management" = @("academic-details.html", "subject-marks.html")
    "Calendar System" = @("daily-calendar.html")
    "Study Tools" = @("flashcards.html", "study-spaces.html", "workspace.html", "grind.html")
    "Assessment" = @("instant-test-feedback.html")
    "Wellness" = @("sleep-saboteurs.html")
    "Configuration" = @("settings.html")
    "Core Application" = @("index.html", "landing.html", "404.html", "extracted.html")
}

foreach ($module in $recommendations.Keys) {
    Write-Host "  🎯 $module Module:" -ForegroundColor Green
    foreach ($file in $recommendations[$module]) {
        if (Test-Path $file) {
            Write-Host "     ✓ $file" -ForegroundColor White
        } else {
            Write-Host "     ✗ $file (not found)" -ForegroundColor Red
        }
    }
    Write-Host ""
}

# 5. Generate migration commands
Write-Host "5. SUGGESTED MIGRATION COMMANDS:" -ForegroundColor Yellow
Write-Host ""
Write-Host "# Create new directory structure" -ForegroundColor Gray
Write-Host "mkdir src\modules\tasks, src\modules\academic, src\modules\calendar, src\modules\study, src\modules\assessment, src\modules\wellness, src\modules\config, src\core, src\shared" -ForegroundColor White
Write-Host ""
Write-Host "# Move files to appropriate modules" -ForegroundColor Gray
Write-Host "move tasks.html src\modules\tasks\" -ForegroundColor White
Write-Host "move academic-details.html src\modules\academic\" -ForegroundColor White
Write-Host "move daily-calendar.html src\modules\calendar\" -ForegroundColor White
Write-Host "# ... (additional move commands)" -ForegroundColor White

Write-Host ""
Write-Host "Analysis complete! Check 'modularization-plan.md' for detailed implementation plan." -ForegroundColor Green
