@echo off
echo ========================================
echo GPAce Codebase Modularization Migration
echo ========================================
echo.

echo Creating backup...
if not exist "backup" mkdir backup
xcopy *.* backup\ /E /I /Y > nul
echo ✓ Backup created in 'backup' folder
echo.

echo Creating new directory structure...

REM Create main directories
mkdir src 2>nul
mkdir src\modules 2>nul
mkdir src\core 2>nul
mkdir src\shared 2>nul
mkdir src\infrastructure 2>nul

REM Create module directories
mkdir src\modules\tasks 2>nul
mkdir src\modules\tasks\css 2>nul
mkdir src\modules\tasks\js 2>nul
mkdir src\modules\tasks\docs 2>nul

mkdir src\modules\academic 2>nul
mkdir src\modules\academic\css 2>nul

mkdir src\modules\calendar 2>nul
mkdir src\modules\calendar\css 2>nul
mkdir src\modules\calendar\data 2>nul

mkdir src\modules\study 2>nul
mkdir src\modules\study\css 2>nul
mkdir src\modules\study\docs 2>nul
mkdir src\modules\study\flashcards 2>nul

mkdir src\modules\assessment 2>nul
mkdir src\modules\assessment\css 2>nul

mkdir src\modules\wellness 2>nul
mkdir src\modules\wellness\css 2>nul

mkdir src\modules\config 2>nul
mkdir src\modules\config\css 2>nul

REM Create shared directories
mkdir src\shared\css 2>nul
mkdir src\shared\js 2>nul
mkdir src\shared\assets 2>nul
mkdir src\shared\data 2>nul

REM Create infrastructure directories
mkdir src\infrastructure\workers 2>nul

echo ✓ Directory structure created
echo.

echo Migrating Task Management Module...
if exist "tasks.html" copy "tasks.html" "src\modules\tasks\index.html" > nul
if exist "priority-calculator.html" copy "priority-calculator.html" "src\modules\tasks\calculator.html" > nul
if exist "priority-list.html" copy "priority-list.html" "src\modules\tasks\list.html" > nul
if exist "priority-calculator.js" copy "priority-calculator.js" "src\modules\tasks\js\calculator.js" > nul
if exist "priority-calculator-with-worker.js" copy "priority-calculator-with-worker.js" "src\modules\tasks\js\calculator-worker.js" > nul
if exist "priority-worker-README.md" copy "priority-worker-README.md" "src\modules\tasks\docs\worker-readme.md" > nul
if exist "css\priority-calculator.css" copy "css\priority-calculator.css" "src\modules\tasks\css\calculator.css" > nul
if exist "css\priority-list.css" copy "css\priority-list.css" "src\modules\tasks\css\list.css" > nul
if exist "css\task-display.css" copy "css\task-display.css" "src\modules\tasks\css\display.css" > nul
if exist "css\task-notes.css" copy "css\task-notes.css" "src\modules\tasks\css\notes.css" > nul
if exist "css\taskLinks.css" copy "css\taskLinks.css" "src\modules\tasks\css\links.css" > nul
echo ✓ Task Management Module migrated

echo Migrating Academic Management Module...
if exist "academic-details.html" copy "academic-details.html" "src\modules\academic\details.html" > nul
if exist "subject-marks.html" copy "subject-marks.html" "src\modules\academic\marks.html" > nul
if exist "css\academic-details.css" copy "css\academic-details.css" "src\modules\academic\css\details.css" > nul
if exist "css\subject-marks.css" copy "css\subject-marks.css" "src\modules\academic\css\marks.css" > nul
echo ✓ Academic Management Module migrated

echo Migrating Calendar Module...
if exist "daily-calendar.html" copy "daily-calendar.html" "src\modules\calendar\daily.html" > nul
if exist "css\daily-calendar.css" copy "css\daily-calendar.css" "src\modules\calendar\css\daily.css" > nul
if exist "data\schedule.json" copy "data\schedule.json" "src\modules\calendar\data\schedule.json" > nul
if exist "data\timetable.json" copy "data\timetable.json" "src\modules\calendar\data\timetable.json" > nul
echo ✓ Calendar Module migrated

echo Migrating Study Tools Module...
if exist "flashcards.html" copy "flashcards.html" "src\modules\study\flashcards.html" > nul
if exist "study-spaces.html" copy "study-spaces.html" "src\modules\study\spaces.html" > nul
if exist "workspace.html" copy "workspace.html" "src\modules\study\workspace.html" > nul
if exist "grind.html" copy "grind.html" "src\modules\study\grind.html" > nul
if exist "grind.css" copy "grind.css" "src\modules\study\css\grind.css" > nul
if exist "css\flashcards.css" copy "css\flashcards.css" "src\modules\study\css\flashcards.css" > nul
if exist "css\study-spaces.css" copy "css\study-spaces.css" "src\modules\study\css\spaces.css" > nul
if exist "css\workspace.css" copy "css\workspace.css" "src\modules\study\css\workspace.css" > nul
if exist "css\text-expansion.css" copy "css\text-expansion.css" "src\modules\study\css\text-expansion.css" > nul
if exist "docs\text-expansion.md" copy "docs\text-expansion.md" "src\modules\study\docs\text-expansion.md" > nul
if exist "flashcards" xcopy "flashcards" "src\modules\study\flashcards" /E /I /Y > nul
echo ✓ Study Tools Module migrated

echo Migrating Assessment Module...
if exist "instant-test-feedback.html" copy "instant-test-feedback.html" "src\modules\assessment\feedback.html" > nul
if exist "css\test-feedback.css" copy "css\test-feedback.css" "src\modules\assessment\css\feedback.css" > nul
echo ✓ Assessment Module migrated

echo Migrating Wellness Module...
if exist "sleep-saboteurs.html" copy "sleep-saboteurs.html" "src\modules\wellness\sleep.html" > nul
if exist "css\sleep-saboteurs.css" copy "css\sleep-saboteurs.css" "src\modules\wellness\css\sleep.css" > nul
echo ✓ Wellness Module migrated

echo Migrating Configuration Module...
if exist "settings.html" copy "settings.html" "src\modules\config\settings.html" > nul
if exist "css\settings.css" copy "css\settings.css" "src\modules\config\css\settings.css" > nul
echo ✓ Configuration Module migrated

echo Migrating Core Application...
if exist "index.html" copy "index.html" "src\core\app.html" > nul
if exist "landing.html" copy "landing.html" "src\core\landing.html" > nul
if exist "404.html" copy "404.html" "src\core\404.html" > nul
if exist "extracted.html" copy "extracted.html" "src\core\extracted.html" > nul
echo ✓ Core Application migrated

echo Migrating Infrastructure...
if exist "server.js" copy "server.js" "src\infrastructure\server.js" > nul
if exist "worker.js" copy "worker.js" "src\infrastructure\workers\main-worker.js" > nul
if exist "test-worker.js" copy "test-worker.js" "src\infrastructure\workers\test-worker.js" > nul
echo ✓ Infrastructure migrated

echo Migrating Shared Resources...
if exist "assets" xcopy "assets" "src\shared\assets" /E /I /Y > nul
if exist "data\locations.json" copy "data\locations.json" "src\shared\data\locations.json" > nul
if exist "data\energy-logs" xcopy "data\energy-logs" "src\shared\data\energy-logs" /E /I /Y > nul
if exist "alarm-sounds" xcopy "alarm-sounds" "src\shared\assets\alarm-sounds" /E /I /Y > nul

REM Copy common CSS files to shared
if exist "css\compact-style.css" copy "css\compact-style.css" "src\shared\css\compact-style.css" > nul
if exist "css\notification.css" copy "css\notification.css" "src\shared\css\notification.css" > nul
if exist "css\sideDrawer.css" copy "css\sideDrawer.css" "src\shared\css\sideDrawer.css" > nul
if exist "css\simulation-enhancer.css" copy "css\simulation-enhancer.css" "src\shared\css\simulation-enhancer.css" > nul
if exist "css\alarm-service.css" copy "css\alarm-service.css" "src\shared\css\alarm-service.css" > nul
if exist "css\ai-search-response.css" copy "css\ai-search-response.css" "src\shared\css\ai-search-response.css" > nul
echo ✓ Shared Resources migrated

echo.
echo ========================================
echo Migration Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Review the migrated files in the 'src' directory
echo 2. Update file paths in HTML and CSS files
echo 3. Test the application functionality
echo 4. Update build scripts and package.json
echo 5. Remove original files after verification
echo.
echo Original files are preserved and backed up.
echo Check 'modularization-plan.md' for detailed next steps.
echo.
pause
